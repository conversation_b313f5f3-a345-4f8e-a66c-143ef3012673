# ZENITH MINING SCRIPT - ORE-SPECIFIC ROCK PROPS UPDATE
**Made by Vexy**

## 🪨 VISUAL MINING ENHANCEMENT
Updated all mining zones to use **ore-specific rock props** instead of generic rocks for a much more immersive and realistic mining experience!

## 📋 ROCK PROP MAPPING

### Before Update:
**All Zones Used Generic Rocks:**
```lua
models = { 'prop_rock_3_b', 'prop_rock_3_d', 'prop_rock_3_f' }
```

### After Update:
**Each Zone Uses Ore-Specific Rocks:**

#### Zone 1: Copper Ore Zone
```lua
models = { 'dl_copper_ore_01.ydr' }
```
- **Visual**: Copper-colored rocks with copper ore veins
- **Ore Type**: Copper Ore
- **Level**: 1

#### Zone 2: Coal Ore Zone  
```lua
models = { 'dl_stone_ore_01.ydr' }
```
- **Visual**: Dark stone rocks suitable for coal mining
- **Ore Type**: Coal Ore
- **Level**: 1

#### Zone 3: Iron Ore Zone
```lua
models = { 'dl_iron_ore_01.ydr' }
```
- **Visual**: Iron-rich rocks with metallic appearance
- **Ore Type**: Iron Ore
- **Level**: 2

#### Zone 4: Silver Ore Zone
```lua
models = { 'dl_silver_ore_01.ydr' }
```
- **Visual**: Silver-tinted rocks with precious metal veins
- **Ore Type**: Silver Ore
- **Level**: 3

#### Zone 5: Gold Ore Zone
```lua
models = { 'dl_gold_ore_01.ydr' }
```
- **Visual**: Gold-flecked rocks with golden ore veins
- **Ore Type**: Gold Ore
- **Level**: 4

#### Zone 6: Mixed Ore Zone (Copper + Coal)
```lua
models = { 'dl_copper_ore_01.ydr', 'dl_stone_ore_01.ydr' }
```
- **Visual**: Mix of copper and coal rocks
- **Ore Types**: Copper Ore (60%) + Coal Ore (40%)
- **Level**: 1

#### Zone 7: Iron & Silver Zone
```lua
models = { 'dl_iron_ore_01.ydr', 'dl_silver_ore_01.ydr' }
```
- **Visual**: Mix of iron and silver rocks
- **Ore Types**: Iron Ore (70%) + Silver Ore (30%)
- **Level**: 2

#### Zone 8: Premium Gold Zone
```lua
models = { 'dl_gold_ore_01.ydr', 'dl_silver_ore_01.ydr' }
```
- **Visual**: Mix of gold and silver rocks
- **Ore Types**: Gold Ore (80%) + Silver Ore (20%)
- **Level**: 3

## 🎯 BENEFITS OF ORE-SPECIFIC ROCKS

### Enhanced Immersion:
- ✅ **Visual Consistency**: Rock appearance matches the ore being mined
- ✅ **Realistic Experience**: Players can visually identify ore types
- ✅ **Professional Look**: Much more polished mining system
- ✅ **Zone Recognition**: Easy to identify different mining areas

### Gameplay Improvements:
- ✅ **Zone Identification**: Players can instantly recognize ore zones
- ✅ **Strategic Planning**: Visual cues help players choose mining locations
- ✅ **Immersive Roleplay**: Rocks look like they actually contain the ores
- ✅ **Server Quality**: Professional appearance enhances server reputation

### Technical Benefits:
- ✅ **Proper Resource Usage**: Using server's existing ore rock props
- ✅ **Optimized Performance**: Single prop per zone (except mixed zones)
- ✅ **Easy Maintenance**: Clear prop-to-ore relationships
- ✅ **Future Expansion**: Easy to add new ore types with matching rocks

## 🗺️ ZONE VISUAL GUIDE

| Zone | Ore Type(s) | Rock Prop | Visual Description |
|------|-------------|-----------|-------------------|
| 1 | Copper | `dl_copper_ore_01.ydr` | Copper-colored rocks |
| 2 | Coal | `dl_stone_ore_01.ydr` | Dark stone rocks |
| 3 | Iron | `dl_iron_ore_01.ydr` | Metallic iron rocks |
| 4 | Silver | `dl_silver_ore_01.ydr` | Silver-tinted rocks |
| 5 | Gold | `dl_gold_ore_01.ydr` | Gold-flecked rocks |
| 6 | Copper + Coal | Both copper & stone | Mixed appearance |
| 7 | Iron + Silver | Both iron & silver | Mixed metallic rocks |
| 8 | Gold + Silver | Both gold & silver | Premium mixed rocks |

## 🔧 TECHNICAL IMPLEMENTATION

### Single Ore Zones (1-5):
- Use one specific rock prop per zone
- Rock appearance perfectly matches ore type
- Consistent visual experience

### Mixed Ore Zones (6-8):
- Use multiple rock props based on ore types
- Script randomly selects appropriate rock for each spawn
- Visual variety matches ore drop chances

## 🎮 PLAYER EXPERIENCE

### What Players Will See:
1. **Copper Zones**: Copper-colored rocks that look like they contain copper
2. **Coal Zones**: Dark, coal-like stone formations
3. **Iron Zones**: Metallic-looking rocks with iron ore appearance
4. **Silver Zones**: Shimmering rocks with silver ore veins
5. **Gold Zones**: Precious-looking rocks with golden flecks
6. **Mixed Zones**: Variety of rocks matching the available ore types

### Gameplay Impact:
- Players can instantly identify what ores they'll find
- More immersive and realistic mining experience
- Professional server appearance
- Enhanced roleplay opportunities

The mining system now provides a **visually stunning and realistic experience** where every rock tells a story about the treasures hidden within! 🎉
