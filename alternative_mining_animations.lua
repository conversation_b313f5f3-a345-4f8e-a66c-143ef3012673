-- ========================================
-- BEST MINING ANIMATIONS FOR FIVEM
-- Made by Vexy
-- ========================================
-- Here are the BEST mining animation options tested and verified
-- Replace the 'anim' line in config/client.lua with any of these:

-- CURRENT (BEST): Ground attack with large weapon - Most realistic mining motion
-- anim = { dict = 'melee@large_wpn@streamed_core', clip = 'ground_attack_on_spot', flag = 1 },

-- OPTION 1: Heavy downward strike (excellent for pickaxe mining)
-- anim = { dict = 'melee@large_wpn@streamed_core', clip = 'heavy_attack_low_l', flag = 1 },

-- OPTION 2: Continuous ground attack (smooth mining motion)
-- anim = { dict = 'melee@large_wpn@streamed_core', clip = 'ground_attack_on_spot', flag = 49 },

-- OPTION 3: Construction drilling (realistic for hard rock)
-- anim = { dict = 'amb@world_human_const_drill@male@drill@base', clip = 'base', flag = 1 },

-- OPTION 4: Hammering motion (good for general mining)
-- anim = { dict = 'amb@world_human_hammering@male@base', clip = 'base', flag = 1 },

-- OPTION 5: Hatchet heavy attack (powerful mining strikes)
-- anim = { dict = 'melee@hatchet@streamed_core', clip = 'plyr_heavy_attack_a', flag = 1 },

-- OPTION 6: Alternative ground attack (different angle)
-- anim = { dict = 'melee@large_wpn@streamed_core', clip = 'heavy_attack_low_r', flag = 1 },

-- ========================================
-- ANIMATION FLAGS EXPLAINED:
-- ========================================
-- flag = 1  : Normal looping animation
-- flag = 2  : Animation plays once then stops
-- flag = 16 : Animation can be cancelled by player movement
-- flag = 32 : Animation loops and can be cancelled
-- flag = 49 : Animation loops, can be cancelled, and allows upper body movement

-- ========================================
-- HOW TO CHANGE:
-- ========================================
-- 1. Open Zenith_mining_script/config/client.lua
-- 2. Find line 16 with the 'anim' configuration
-- 3. Replace it with one of the options above
-- 4. Restart the script to test the new animation
-- 5. Choose the one that looks best for your server!

-- ========================================
-- PROP ADJUSTMENTS:
-- ========================================
-- If the pickaxe doesn't align properly with the new animation,
-- you may need to adjust the prop position and rotation:
-- 
-- prop = { 
--     bone = 28422, 
--     model = 'prop_tool_pickaxe', 
--     pos = vec3(0.09, -0.05, -0.02),    -- X, Y, Z position
--     rot = vec3(-78.0, 13.0, 28.0)      -- X, Y, Z rotation
-- }
--
-- Adjust these values to make the pickaxe look natural in the player's hands
