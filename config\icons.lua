return {

    -- Interactions
    mine = 'fas fa-hammer',
    mine_color = '#FF8C00',

    smelt = 'fas fa-fire',
    smelt_color = '#FF6600',

    mine_shop = 'fas fa-comment',
    mine_shop_color = '#FF7F00',

    -- Player data menu
    player_level = 'fas fa-chart-simple',
    player_level_color = '#FF8C00',

    view_stats = 'fas fa-chart-pie',
    view_stats_color = '#FF7F00',

    -- Stats menu
    stats_mined = 'fas fa-hammer',
    stats_mined_color = '#FF8C00',

    stats_smelted = 'fas fa-fire',
    stats_smelted_color = '#FF6600',

    stats_earned = 'fas fa-dollar-sign',
    stats_earned_color = '#FF7F00',

    -- The Mines menu
    mines_leaderboard = 'fas fa-ranking-star',
    mines_leaderboard_color = '#FF8C00',

    mines_shop = 'fas fa-hammer',
    mines_shop_color = '#FF7F00',

    mines_pawn = 'fas fa-sack-dollar',
    mines_pawn_color = '#FF6600',

    leaderboard = 'fas fa-trophy',
    leaderboard_color = '#FF8C00',

    -- Input menu(s)
    input_quantity = 'fas fa-hashtag',
    input_quantity_color = '#FF7F00',

    -- Text UI
    smelting = 'fas fa-fire',
    smelting_color = '#FF6600'

}